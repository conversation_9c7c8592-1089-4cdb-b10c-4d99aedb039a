# 导入必要的库
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from IPython.display import HTML
import math

# 导入我们的模拟器
from oblique_collision_simulator import ObliqueCollisionSimulator

# 设置matplotlib显示中文
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 创建模拟器
sim1 = ObliqueCollisionSimulator(m1=1.0, m2=1.0, dt=0.01, total_time=3.0)

# 设置初始条件（斜碰场景）
print("完全弹性斜碰 - 初始条件:")
print("质点1: 位置(0, 0), 速度(1.2, 0.8), 质量=1.0")
print("质点2: 位置(1.8, 0.5), 速度(-0.9, -0.3), 质量=1.0")

sim1.set_initial_conditions(
    x1=0.0, y1=0.0, vx1=1.2, vy1=0.8,
    x2=1.8, y2=0.5, vx2=-0.9, vy2=-0.3
)

# 运行完全弹性碰撞模拟
sim1.simulate('elastic')

# 创建动画
ani1 = sim1.create_animation("完全弹性斜碰")

# 显示动画
HTML(ani1.to_jshtml())

# 创建模拟器
sim2 = ObliqueCollisionSimulator(m1=1.0, m2=1.5, dt=0.01, total_time=3.0)

# 设置相同的初始条件
print("非弹性斜碰 (e=0.6) - 初始条件:")
print("质点1: 位置(0, 0), 速度(1.2, 0.8), 质量=1.0")
print("质点2: 位置(1.8, 0.5), 速度(-0.9, -0.3), 质量=1.5")

sim2.set_initial_conditions(
    x1=0.0, y1=0.0, vx1=1.2, vy1=0.8,
    x2=1.8, y2=0.5, vx2=-0.9, vy2=-0.3
)

# 运行非弹性碰撞模拟（恢复系数e=0.6）
sim2.simulate('inelastic', e=0.6)

# 创建动画
ani2 = sim2.create_animation("非弹性斜碰 (e=0.6)")

# 显示动画
HTML(ani2.to_jshtml())

# 创建模拟器
sim3 = ObliqueCollisionSimulator(m1=1.0, m2=1.5, dt=0.01, total_time=3.0)

# 设置相同的初始条件
print("完全非弹性斜碰 - 初始条件:")
print("质点1: 位置(0, 0), 速度(1.2, 0.8), 质量=1.0")
print("质点2: 位置(1.8, 0.5), 速度(-0.9, -0.3), 质量=1.5")

sim3.set_initial_conditions(
    x1=0.0, y1=0.0, vx1=1.2, vy1=0.8,
    x2=1.8, y2=0.5, vx2=-0.9, vy2=-0.3
)

# 运行完全非弹性碰撞模拟
sim3.simulate('completely_inelastic')

# 创建动画
ani3 = sim3.create_animation("完全非弹性斜碰")

# 显示动画
HTML(ani3.to_jshtml())

def create_comparison_animation():
    """创建三种碰撞类型的对比动画"""
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    # 相同的初始条件
    initial_conditions = {
        'x1': 0.0, 'y1': 0.0, 'vx1': 1.0, 'vy1': 0.5,
        'x2': 1.5, 'y2': 0.3, 'vx2': -0.8, 'vy2': -0.2
    }
    
    collision_types = ['elastic', 'inelastic', 'completely_inelastic']
    titles = ['完全弹性碰撞', '非弹性碰撞 (e=0.6)', '完全非弹性碰撞']
    
    simulators = []
    animations_data = []
    
    for i, (collision_type, title) in enumerate(zip(collision_types, titles)):
        # 创建模拟器
        sim = ObliqueCollisionSimulator()
        sim.set_initial_conditions(**initial_conditions)
        
        # 运行模拟
        if collision_type == 'inelastic':
            sim.simulate(collision_type, e=0.6)
        else:
            sim.simulate(collision_type)
            
        simulators.append(sim)
        
        # 设置子图
        ax = axes[i]
        x_min = min(np.min(sim.x1_vals), np.min(sim.x2_vals)) - 0.3
        x_max = max(np.max(sim.x1_vals), np.max(sim.x2_vals)) + 0.3
        y_min = min(np.min(sim.y1_vals), np.min(sim.y2_vals)) - 0.3
        y_max = max(np.max(sim.y1_vals), np.max(sim.y2_vals)) + 0.3
        
        ax.set_xlim(x_min, x_max)
        ax.set_ylim(y_min, y_max)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.set_xlabel('X 位置')
        ax.set_ylabel('Y 位置')
        ax.set_title(title)
        
        # 创建质点和轨迹
        atom1, = ax.plot([], [], 'bo', ms=12, label='质点1')
        atom2, = ax.plot([], [], 'ro', ms=12, label='质点2')
        trail1, = ax.plot([], [], 'b-', alpha=0.3, linewidth=1)
        trail2, = ax.plot([], [], 'r-', alpha=0.3, linewidth=1)
        
        if i == 0:  # 只在第一个子图显示图例
            ax.legend()
            
        animations_data.append((atom1, atom2, trail1, trail2, sim))
    
    def init():
        elements = []
        for atom1, atom2, trail1, trail2, _ in animations_data:
            atom1.set_data([], [])
            atom2.set_data([], [])
            trail1.set_data([], [])
            trail2.set_data([], [])
            elements.extend([atom1, atom2, trail1, trail2])
        return elements
    
    def update(frame):
        elements = []
        for atom1, atom2, trail1, trail2, sim in animations_data:
            # 更新质点位置
            atom1.set_data(sim.x1_vals[frame], sim.y1_vals[frame])
            atom2.set_data(sim.x2_vals[frame], sim.y2_vals[frame])
            
            # 更新轨迹
            trail_length = min(frame + 1, 30)
            start_idx = max(0, frame + 1 - trail_length)
            trail1.set_data(sim.x1_vals[start_idx:frame+1], sim.y1_vals[start_idx:frame+1])
            trail2.set_data(sim.x2_vals[start_idx:frame+1], sim.y2_vals[start_idx:frame+1])
            
            elements.extend([atom1, atom2, trail1, trail2])
        return elements
    
    ani = animation.FuncAnimation(fig, update, frames=simulators[0].num_steps,
                                init_func=init, blit=True, interval=50)
    plt.tight_layout()
    return ani

# 创建对比动画
comparison_ani = create_comparison_animation()

# 显示动画
HTML(comparison_ani.to_jshtml())